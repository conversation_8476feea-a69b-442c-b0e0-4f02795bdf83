"""
Test Azure Revenue Growth Enhancement

This test validates that the system correctly handles queries like
"Azure revenue growth for Microsoft in 2025" by:
1. Detecting segment specificity
2. Routing to segment analysis agent
3. Fetching SEC filing data
4. Extracting Azure-specific revenue data
5. Providing 2025 guidance and projections
"""

import asyncio
import pytest
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime

# Import the enhanced components
from src.intent_analyzer import IntentAnalyzer, QueryIntent
from src.financial_analyst import FinancialAnalyst
from src.segment_analysis_agent import SegmentAnalysisAgent
from src.sec_data_provider import EnhancedSECDataProvider
from src.function_calling import FunctionCallOrchestrator
from config import Config


class TestAzureRevenueEnhancement:
    """Test suite for Azure revenue growth query enhancements"""
    
    @pytest.fixture
    def mock_config(self):
        """Mock configuration"""
        config = Mock(spec=Config)
        config.aws_region = "us-east-1"
        config.bedrock_model_id = "anthropic.claude-3-5-sonnet-********-v2:0"
        return config
    
    @pytest.fixture
    def mock_bedrock_client(self):
        """Mock Bedrock client"""
        client = Mock()
        client.converse_with_functions = AsyncMock()
        client.converse_async = AsyncMock()
        client.health_check = AsyncMock(return_value=True)
        return client
    
    @pytest.fixture
    def mock_data_provider(self):
        """Mock data provider"""
        provider = Mock()
        provider.__aenter__ = AsyncMock(return_value=provider)
        provider.__aexit__ = AsyncMock(return_value=None)
        provider.validate_ticker = AsyncMock(return_value=True)
        return provider

    @pytest.mark.asyncio
    async def test_intent_analysis_detects_azure_segment(self, mock_bedrock_client):
        """Test that intent analyzer detects Azure segment queries"""
        
        # Mock function calling response for Azure query
        mock_bedrock_client.converse_with_functions.return_value = (
            "Analysis complete",
            [{
                'name': 'analyze_query_intent',
                'input': {
                    'primary_intent': 'fundamental',
                    'tickers': ['MSFT'],
                    'confidence': 0.9,
                    'analysis_types': ['fundamental', 'segment_analysis'],
                    'temporal_specificity': True,
                    'temporal_periods': ['2025'],
                    'comparison_mode': False,
                    'specific_metrics': ['revenue growth', 'Azure revenue'],
                    'business_segments': ['Azure'],
                    'segment_specificity': True,
                    'requires_sec_filings': True
                }
            }],
            {'total_cost': 0.01}
        )
        
        analyzer = IntentAnalyzer(mock_bedrock_client)
        
        query = "Azure revenue growth for Microsoft in 2025"
        intent = await analyzer.analyze_query(query)
        
        # Verify segment detection
        assert intent.segment_specificity == True
        assert 'Azure' in intent.business_segments
        assert intent.requires_sec_filings == True
        assert 'segment_analysis' in intent.analysis_types
        assert '2025' in intent.temporal_periods
        assert intent.temporal_specificity == True
        assert 'MSFT' in intent.tickers

    @pytest.mark.asyncio
    async def test_fallback_intent_analysis_detects_azure(self, mock_bedrock_client):
        """Test fallback analysis detects Azure segment"""
        
        # Mock function calling to fail, triggering fallback
        mock_bedrock_client.converse_with_functions.return_value = (
            "No function calls",
            [],
            {'total_cost': 0.01}
        )
        
        analyzer = IntentAnalyzer(mock_bedrock_client)
        
        query = "Azure revenue growth for Microsoft in 2025"
        intent = await analyzer.analyze_query(query)
        
        # Verify fallback detection
        assert intent.segment_specificity == True
        assert 'Azure' in intent.business_segments
        assert intent.requires_sec_filings == True
        assert 'segment_analysis' in intent.analysis_types
        assert '2025' in intent.temporal_periods
        assert intent.temporal_specificity == True
        assert 'MSFT' in intent.tickers

    @pytest.mark.asyncio
    async def test_sec_provider_extracts_azure_data(self, mock_config):
        """Test SEC provider extracts Azure-specific data"""
        
        with patch('src.sec_data_provider.Company') as mock_company:
            # Mock filing data
            mock_filing = Mock()
            mock_filing.text.return_value = """
            Intelligent Cloud segment revenue was $24.3 billion, up 20% year-over-year.
            Azure revenue grew 29% year-over-year. Azure consumption grew 28%.
            For fiscal year 2025, we expect Azure revenue to grow 25-30%.
            """
            mock_filing.filing_date = datetime(2024, 10, 24)
            
            mock_company_instance = Mock()
            mock_company_instance.get_filings.return_value.latest.return_value = [mock_filing]
            mock_company.return_value = mock_company_instance
            
            sec_provider = EnhancedSECDataProvider(mock_config)
            
            # Test segment data extraction
            segment_data = await sec_provider.get_segment_data("MSFT", "Azure", "latest")
            
            assert segment_data['ticker'] == "MSFT"
            assert segment_data['segment_name'] == "Azure"
            assert 'segment_data' in segment_data

    @pytest.mark.asyncio
    async def test_future_period_guidance_extraction(self, mock_config):
        """Test extraction of 2025 guidance for Azure"""
        
        with patch('src.sec_data_provider.Company') as mock_company:
            # Mock filing with 2025 guidance
            mock_filing = Mock()
            mock_filing.text.return_value = """
            Management Discussion and Analysis:
            Looking ahead to fiscal year 2025, we expect Azure revenue to grow 25-30%.
            We anticipate continued strong demand for cloud services.
            Our target is to reach $30 billion in Azure revenue by 2025.
            """
            mock_filing.filing_date = datetime(2024, 10, 24)
            
            mock_company_instance = Mock()
            mock_company_instance.get_filings.return_value.latest.return_value = [mock_filing]
            mock_company.return_value = mock_company_instance
            
            sec_provider = EnhancedSECDataProvider(mock_config)
            
            # Test future period analysis
            future_analysis = await sec_provider.get_future_period_analysis("MSFT", "2025", "Azure")
            
            assert future_analysis['ticker'] == "MSFT"
            assert future_analysis['target_year'] == "2025"
            assert future_analysis['segment_name'] == "Azure"

    @pytest.mark.asyncio
    async def test_segment_analysis_agent(self, mock_bedrock_client, mock_data_provider, mock_config):
        """Test segment analysis agent handles Azure queries"""
        
        # Mock Bedrock response for segment analysis
        mock_bedrock_client.converse_async.return_value = {
            'output': {
                'message': {
                    'content': [{
                        'text': """AZURE SEGMENT ANALYSIS:
                        
                        Revenue Performance: Azure revenue reached $24.3B in latest quarter
                        Growth Rate: 29% year-over-year growth
                        Market Position: #2 cloud provider globally
                        2025 Outlook: Management expects 25-30% growth
                        """
                    }]
                }
            }
        }
        
        with patch('src.segment_analysis_agent.EnhancedSECDataProvider') as mock_sec:
            # Mock SEC data
            mock_sec_instance = Mock()
            mock_sec_instance.get_segment_data.return_value = {
                'ticker': 'MSFT',
                'segment_name': 'Azure',
                'segment_data': [{
                    'revenue': 24300000000,
                    'filing_date': '2024-10-24',
                    'form_type': '10-Q'
                }]
            }
            mock_sec_instance.get_future_period_analysis.return_value = {
                'guidance_data': {
                    '2024-10-24': {
                        '2025_growth_rate': '25-30%'
                    }
                }
            }
            mock_sec.return_value = mock_sec_instance
            
            agent = SegmentAnalysisAgent(mock_bedrock_client, mock_data_provider, mock_config)
            
            result = await agent.analyze_segment("MSFT", "Azure", "2025")
            
            assert result.segment_name == "Azure"
            assert result.ticker == "MSFT"
            assert result.confidence > 0.8
            assert 'revenue' in str(result.revenue_data).lower()

    @pytest.mark.asyncio
    async def test_function_calling_segment_functions(self, mock_bedrock_client, mock_data_provider):
        """Test new segment analysis function calling tools"""
        
        orchestrator = FunctionCallOrchestrator(mock_bedrock_client, mock_data_provider)
        
        # Verify segment functions are available
        function_names = [tool['toolSpec']['name'] for tool in orchestrator.function_tools]
        
        assert 'get_segment_revenue_data' in function_names
        assert 'get_future_period_guidance' in function_names
        
        # Test function execution would require more complex mocking
        # This verifies the functions are properly registered

    @pytest.mark.asyncio
    async def test_end_to_end_azure_query(self, mock_config, mock_bedrock_client, mock_data_provider):
        """Test complete end-to-end Azure revenue query processing"""
        
        # This would be a comprehensive integration test
        # For now, verify the components are properly connected
        
        with patch('src.financial_analyst.BedrockClient', return_value=mock_bedrock_client):
            with patch('src.financial_analyst.FinancialDataProvider', return_value=mock_data_provider):
                
                # Mock intent analysis
                mock_bedrock_client.converse_with_functions.return_value = (
                    "Analysis complete",
                    [{
                        'name': 'analyze_query_intent',
                        'input': {
                            'primary_intent': 'fundamental',
                            'tickers': ['MSFT'],
                            'confidence': 0.9,
                            'analysis_types': ['segment_analysis'],
                            'business_segments': ['Azure'],
                            'segment_specificity': True,
                            'temporal_periods': ['2025'],
                            'temporal_specificity': True,
                            'requires_sec_filings': True
                        }
                    }],
                    {'total_cost': 0.01}
                )
                
                analyst = FinancialAnalyst(mock_config)
                await analyst.initialize()
                
                # Verify initialization includes segment analysis capability
                assert analyst.smart_orchestrator is not None
                assert analyst.intent_analyzer is not None

    def test_query_intent_dataclass_enhancements(self):
        """Test QueryIntent dataclass has new segment fields"""
        
        intent = QueryIntent(
            primary_intent="fundamental",
            secondary_intents=[],
            tickers=["MSFT"],
            confidence=0.9,
            analysis_types=["segment_analysis"],
            business_segments=["Azure"],
            segment_specificity=True,
            requires_sec_filings=True,
            temporal_periods=["2025"],
            temporal_specificity=True
        )
        
        assert hasattr(intent, 'business_segments')
        assert hasattr(intent, 'segment_specificity')
        assert hasattr(intent, 'requires_sec_filings')
        assert intent.business_segments == ["Azure"]
        assert intent.segment_specificity == True
        assert intent.requires_sec_filings == True


if __name__ == "__main__":
    # Run specific test
    test = TestAzureRevenueEnhancement()
    
    # Test intent analysis
    print("Testing intent analysis...")
    # Would need to run with proper async setup
    
    print("All enhancement tests defined successfully!")
