#!/usr/bin/env python3
"""
FinMAS Enhanced - Command Line Financial Analysis System

A dynamic, LLM-driven financial analysis system using AWS Bedrock
with function calling capabilities for comprehensive market analysis.
"""

import os
import sys
import asyncio
import signal
from typing import Optional, Dict, Any
from pathlib import Path

# Add current directory to path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from src.financial_analyst import FinancialAnalyst
from config import Config
from utils.logging_utils import setup_logging, get_logger


class FinancialAnalysisCLI:
    """Command-line interface for financial analysis"""
    
    def __init__(self):
        self.config = Config()
        self.logger = setup_logging()
        self.analyst: Optional[FinancialAnalyst] = None
        self.running = True
        
        # Setup signal handlers for graceful shutdown
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        print("\n\n👋 Shutting down FinMAS Enhanced...")
        self.running = False
        if self.analyst:
            self.analyst.cleanup()
        sys.exit(0)
    
    def _print_banner(self):
        """Print the application banner"""
        banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                          FinMAS Enhanced v2.0                               ║
║                    AI-Powered Financial Analysis System                      ║
║                                                                              ║
║  🤖 LLM-Driven Intelligence  📊 Multi-Agent Architecture                    ║
║  🔄 Function Calling         💰 Real-time Data                              ║
║  💬 Natural Language         📈 Comprehensive Analysis                      ║
╚══════════════════════════════════════════════════════════════════════════════╝

💡 Ask me anything about stocks in natural language!

Examples:
  • "Analyze Apple stock"
  • "What's the technical outlook for Microsoft?"
  • "Compare Tesla vs Ford"
  • "Should I invest in Amazon?"
  • "What are the fundamentals of Google?"

Type 'help' for commands, 'quit' to exit.
"""
        print(banner)
    
    def _print_help(self):
        """Print help information"""
        help_text = """
🔧 Available Commands:
  help          - Show this help message
  quit/exit     - Exit the application
  clear         - Clear conversation history
  cost          - Show session cost summary
  config        - Show current configuration
  
💬 Natural Language Queries:
  Just type your question about stocks naturally!
  
  Analysis Types:
  • Comprehensive: "Analyze [TICKER]"
  • Technical: "Technical analysis of [TICKER]"
  • Fundamental: "Fundamentals of [TICKER]"
  • News: "Recent news on [TICKER]"
  • Comparison: "Compare [TICKER1] vs [TICKER2]"
  • Investment: "Should I buy [TICKER]?"
  
📊 Features:
  • Real-time market data
  • Multi-agent collaboration
  • Cost tracking
  • Conversation context
  • Dynamic analysis
"""
        print(help_text)
    
    def _print_config(self):
        """Print current configuration"""
        config_info = f"""
⚙️ Current Configuration:
  Model: {self.config.bedrock_model}
  Region: {self.config.aws_region}
  Temperature: {self.config.temperature}
  Max Tokens: {self.config.max_tokens}
  
💰 Cost Tracking:
  Budget Limit: ${self.config.budget_limit:.2f}
  Current Session: ${self.logger.cost_tracker.total_cost:.4f}
"""
        print(config_info)
    
    def _print_cost_summary(self):
        """Print session cost summary"""
        cost_summary = self.logger.cost_tracker.get_session_summary()
        
        cost_info = f"""
💰 Session Cost Summary:
  Total Cost: ${cost_summary['total_cost']:.4f}
  API Calls: {cost_summary['total_calls']}
  Total Tokens: {cost_summary['total_tokens']:,}
  Average Cost/Call: ${cost_summary['average_cost_per_call']:.4f}
  
📊 Efficiency:
  Tokens per Dollar: {cost_summary['total_tokens'] / max(cost_summary['total_cost'], 0.0001):.0f}
"""
        
        if cost_summary.get('model_breakdown'):
            cost_info += "\n🔧 Model Breakdown:\n"
            for model, cost in cost_summary['model_breakdown'].items():
                model_name = model.split('.')[-1] if '.' in model else model
                cost_info += f"  {model_name}: ${cost:.4f}\n"
        
        print(cost_info)
    
    async def initialize(self):
        """Initialize the financial analyst"""
        try:
            print("🚀 Initializing FinMAS Enhanced...")
            print("🔧 Setting up AWS Bedrock connection...")
            
            self.analyst = FinancialAnalyst(self.config)
            await self.analyst.initialize()
            
            print("✅ System ready!")
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize: {str(e)}")
            print("\n💡 Troubleshooting:")
            print("  1. Check AWS credentials are configured")
            print("  2. Verify AWS Bedrock access permissions")
            print("  3. Ensure internet connectivity")
            return False
    
    async def process_query(self, query: str) -> bool:
        """Process a user query"""
        if not self.analyst:
            print("❌ System not initialized. Please restart.")
            return False
        
        try:
            print(f"\n🤔 Analyzing: {query}")
            print("⏳ Processing...")
            
            # Get response from the analyst
            response, cost_info = await self.analyst.analyze_query(query)
            
            # Display response
            print("\n" + "="*80)
            print(response)
            print("="*80)
            
            # Display cost information
            if cost_info:
                cost_text = f"\n💰 Cost: ${cost_info.get('total_cost', 0):.4f} | "
                cost_text += f"Tokens: {cost_info.get('total_tokens', 0):,} | "
                cost_text += f"Duration: {cost_info.get('duration_ms', 0):.0f}ms"
                
                if cost_info.get('function_calls', 0) > 0:
                    cost_text += f" | Functions: {cost_info['function_calls']}"
                
                print(cost_text)
            
            return True
            
        except Exception as e:
            print(f"❌ Error processing query: {str(e)}")
            self.logger.logger.error(f"Query processing error: {str(e)}")
            return False
    
    async def run(self):
        """Main application loop"""
        self._print_banner()
        
        # Initialize the system
        if not await self.initialize():
            return
        
        print("\n💬 Ready for your questions! (Type 'help' for commands)")
        
        while self.running:
            try:
                # Get user input
                query = input("\n🔍 Ask me about stocks: ").strip()
                
                if not query:
                    continue
                
                # Handle commands
                query_lower = query.lower()
                
                if query_lower in ['quit', 'exit', 'q']:
                    break
                
                elif query_lower == 'help':
                    self._print_help()
                    continue
                
                elif query_lower == 'clear':
                    if self.analyst:
                        self.analyst.clear_conversation()
                    print("✅ Conversation history cleared!")
                    continue
                
                elif query_lower == 'cost':
                    self._print_cost_summary()
                    continue
                
                elif query_lower == 'config':
                    self._print_config()
                    continue
                
                # Process financial query
                await self.process_query(query)
                
            except KeyboardInterrupt:
                break
            except EOFError:
                break
            except Exception as e:
                print(f"❌ Unexpected error: {str(e)}")
                self.logger.logger.error(f"Unexpected error in main loop: {str(e)}")
        
        # Cleanup
        print("\n👋 Thank you for using FinMAS Enhanced!")
        if self.analyst:
            self.analyst.cleanup()


def main():
    """Main entry point"""
    # Create config and validate AWS credentials
    config = Config()

    if not config.validate():
        print("❌ Invalid configuration:")
        if not config.aws_access_key_id or not config.aws_secret_access_key:
            print("  - Missing AWS credentials")
            print("\n💡 Please set your AWS credentials:")
            print("  export AWS_ACCESS_KEY_ID=your_access_key")
            print("  export AWS_SECRET_ACCESS_KEY=your_secret_key")
            print("  export AWS_DEFAULT_REGION=us-east-1  # optional")
        if config.temperature < 0 or config.temperature > 1:
            print(f"  - Invalid temperature: {config.temperature} (must be 0-1)")
        if config.max_tokens < 100 or config.max_tokens > 8000:
            print(f"  - Invalid max_tokens: {config.max_tokens} (must be 100-8000)")
        sys.exit(1)

    # Run the CLI
    cli = FinancialAnalysisCLI()

    try:
        asyncio.run(cli.run())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"❌ Fatal error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
