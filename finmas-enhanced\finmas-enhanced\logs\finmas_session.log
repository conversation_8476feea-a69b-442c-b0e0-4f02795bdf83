2025-06-21 15:06:38,049 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_150638
2025-06-21 15:06:38,051 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_150638
2025-06-21 15:06:38,051 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 15:06:38,207 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 15:06:39,660 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (16+4=20 tokens, $0.0001, 1449.86ms)
2025-06-21 15:06:39,662 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 15:06:39,671 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:06:39,671 - finmas_enhanced - INFO - Agent Coordinator initialized with 11 total agents (6 original + 5 specialized)
2025-06-21 15:06:39,671 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 15:07:09,188 - finmas_enhanced - INFO - Analyzing query: What was the average revenue per user (ARPU)? for nrflex and meta...
2025-06-21 15:07:09,189 - finmas_enhanced - INFO - Analyzing query intent: What was the average revenue per user (ARPU)? for nrflex and meta...
2025-06-21 15:07:14,614 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1466+266=1732 tokens, $0.0084, 5414.82ms) with 1 function calls
2025-06-21 15:07:14,615 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1466+266=1732, Cost: $0.0084
2025-06-21 15:07:14,615 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['NFLX', 'META'], confidence: 0.9
2025-06-21 15:07:14,615 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['NFLX', 'META'], confidence: 0.9
2025-06-21 15:07:14,615 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:07:15,639 - finmas_enhanced - INFO - Ticker NFLX validated successfully
2025-06-21 15:07:15,985 - finmas_enhanced - INFO - Ticker META validated successfully
2025-06-21 15:07:15,985 - finmas_enhanced - INFO - Validated tickers: ['NFLX', 'META'] -> ['NFLX', 'META']
2025-06-21 15:07:15,985 - finmas_enhanced - INFO - Starting collaborative analysis for NFLX with agents: ['technical', 'fundamental', 'market']
2025-06-21 15:07:15,985 - finmas_enhanced - INFO - Technical Analyst analyzing NFLX
2025-06-21 15:07:15,985 - finmas_enhanced - INFO - Fetching stock data for NFLX
2025-06-21 15:07:16,125 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/NFLX/info - SUCCESS (139.41ms)
2025-06-21 15:07:16,125 - finmas_enhanced - INFO - Stock data retrieved for NFLX: $1231.41
2025-06-21 15:07:16,125 - finmas_enhanced - INFO - Calculating technical indicators for NFLX
2025-06-21 15:07:16,440 - finmas_enhanced - INFO - Technical indicators calculated for NFLX
2025-06-21 15:07:16,440 - finmas_enhanced - INFO - Fundamental Analyst analyzing NFLX
2025-06-21 15:07:16,440 - finmas_enhanced - INFO - Fetching stock data for NFLX
2025-06-21 15:07:16,587 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/NFLX/info - SUCCESS (136.78ms)
2025-06-21 15:07:16,587 - finmas_enhanced - INFO - Stock data retrieved for NFLX: $1231.41
2025-06-21 15:07:16,591 - finmas_enhanced - INFO - Fetching financial metrics for NFLX
2025-06-21 15:07:17,371 - finmas_enhanced - INFO - Financial metrics retrieved for NFLX
2025-06-21 15:07:17,371 - finmas_enhanced - ERROR - Fundamental analysis failed for NFLX: unsupported operand type(s) for *: 'NoneType' and 'int'
2025-06-21 15:07:17,371 - finmas_enhanced - INFO - Market Analyst analyzing NFLX
2025-06-21 15:07:17,371 - finmas_enhanced - INFO - Fetching stock data for NFLX
2025-06-21 15:07:17,567 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/NFLX/info - SUCCESS (195.75ms)
2025-06-21 15:07:17,567 - finmas_enhanced - INFO - Stock data retrieved for NFLX: $1231.41
2025-06-21 15:07:37,370 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (296+707=1003 tokens, $0.0115, 19803.50ms)
2025-06-21 15:07:37,370 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 296+707=1003, Cost: $0.0115
2025-06-21 15:07:55,820 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (578+817=1395 tokens, $0.0140, 39380.41ms)
2025-06-21 15:07:55,820 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 578+817=1395, Cost: $0.0140
2025-06-21 15:07:55,820 - finmas_enhanced - INFO - Starting collaborative analysis for META with agents: ['technical', 'fundamental', 'market']
2025-06-21 15:07:55,820 - finmas_enhanced - INFO - Technical Analyst analyzing META
2025-06-21 15:07:55,820 - finmas_enhanced - INFO - Fetching stock data for META
2025-06-21 15:07:56,040 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/META/info - SUCCESS (219.41ms)
2025-06-21 15:07:56,040 - finmas_enhanced - INFO - Stock data retrieved for META: $682.35
2025-06-21 15:07:56,040 - finmas_enhanced - INFO - Calculating technical indicators for META
2025-06-21 15:07:56,335 - finmas_enhanced - INFO - Technical indicators calculated for META
2025-06-21 15:07:56,335 - finmas_enhanced - INFO - Fundamental Analyst analyzing META
2025-06-21 15:07:56,335 - finmas_enhanced - INFO - Fetching stock data for META
2025-06-21 15:07:56,476 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/META/info - SUCCESS (141.62ms)
2025-06-21 15:07:56,476 - finmas_enhanced - INFO - Stock data retrieved for META: $682.35
2025-06-21 15:07:56,477 - finmas_enhanced - INFO - Fetching financial metrics for META
2025-06-21 15:07:57,395 - finmas_enhanced - INFO - Financial metrics retrieved for META
2025-06-21 15:07:57,395 - finmas_enhanced - INFO - Market Analyst analyzing META
2025-06-21 15:07:57,399 - finmas_enhanced - INFO - Fetching stock data for META
2025-06-21 15:07:57,604 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/META/info - SUCCESS (205.27ms)
2025-06-21 15:07:57,606 - finmas_enhanced - INFO - Stock data retrieved for META: $682.35
2025-06-21 15:08:15,925 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (583+809=1392 tokens, $0.0139, 19590.14ms)
2025-06-21 15:08:15,925 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 583+809=1392, Cost: $0.0139
2025-06-21 15:08:16,452 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (481+819=1300 tokens, $0.0137, 19053.37ms)
2025-06-21 15:08:16,452 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 481+819=1300, Cost: $0.0137
2025-06-21 15:08:18,968 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (293+675=968 tokens, $0.0110, 21359.71ms)
2025-06-21 15:08:18,968 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 293+675=968, Cost: $0.0110
2025-06-21 15:08:41,873 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (905+641=1546 tokens, $0.0123, 22904.90ms)
2025-06-21 15:08:41,873 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 905+641=1546, Cost: $0.0123
2025-06-21 15:09:46,405 - finmas_enhanced - INFO - Analyzing query: Is R&D spend rising as % of revenue? for nike...
2025-06-21 15:09:46,405 - finmas_enhanced - INFO - Analyzing query intent: Is R&D spend rising as % of revenue? for nike...
2025-06-21 15:09:52,019 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1459+304=1763 tokens, $0.0089, 5613.44ms) with 1 function calls
2025-06-21 15:09:52,020 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1459+304=1763, Cost: $0.0089
2025-06-21 15:09:52,020 - finmas_enhanced - INFO - Intent analysis complete: fundamental, tickers: ['NKE'], confidence: 0.95
2025-06-21 15:09:52,020 - finmas_enhanced - INFO - Intent analysis: fundamental, tickers: ['NKE'], confidence: 0.95
2025-06-21 15:09:52,021 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:09:52,412 - finmas_enhanced - INFO - Ticker NKE validated successfully
2025-06-21 15:09:52,412 - finmas_enhanced - INFO - Validated tickers: ['NKE'] -> ['NKE']
2025-06-21 15:09:52,412 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: fundamental
2025-06-21 15:09:52,412 - finmas_enhanced - INFO - Starting dynamic analysis for NKE with primary agent: fundamental
2025-06-21 15:09:52,412 - finmas_enhanced - INFO - Fundamental Analyst analyzing NKE
2025-06-21 15:09:52,412 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 15:09:52,554 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/NKE/info - SUCCESS (141.88ms)
2025-06-21 15:09:52,555 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 15:09:52,555 - finmas_enhanced - INFO - Fetching financial metrics for NKE
2025-06-21 15:09:53,291 - finmas_enhanced - INFO - Financial metrics retrieved for NKE
2025-06-21 15:10:11,184 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (483+701=1184 tokens, $0.0120, 17893.04ms)
2025-06-21 15:10:11,184 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 483+701=1184, Cost: $0.0120
2025-06-21 15:10:11,184 - finmas_enhanced - INFO - Primary agent fundamental completed analysis
2025-06-21 15:10:11,184 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:10:11,184 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:10:11,184 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:10:30,516 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (863+811=1674 tokens, $0.0148, 19330.86ms)
2025-06-21 15:10:30,516 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 863+811=1674, Cost: $0.0148
2025-06-21 15:10:30,516 - finmas_enhanced - INFO - Synthesis completed for NKE - Total cost: $0.0267
2025-06-21 15:12:31,009 - finmas_enhanced - INFO - Analyzing query: gcp revenue growth for google for 10k docs...
2025-06-21 15:12:31,009 - finmas_enhanced - INFO - Analyzing query intent: gcp revenue growth for google for 10k docs...
2025-06-21 15:12:37,062 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1457+305=1762 tokens, $0.0089, 6052.50ms) with 1 function calls
2025-06-21 15:12:37,062 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1457+305=1762, Cost: $0.0089
2025-06-21 15:12:37,062 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:12:37,066 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:12:37,066 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:12:37,735 - finmas_enhanced - INFO - Ticker GOOGL validated successfully
2025-06-21 15:12:37,735 - finmas_enhanced - INFO - Validated tickers: ['GOOGL'] -> ['GOOGL']
2025-06-21 15:12:37,735 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: segment_analysis
2025-06-21 15:12:37,735 - finmas_enhanced - INFO - Starting dynamic analysis for GOOGL with primary agent: segment_analysis
2025-06-21 15:12:38,456 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:12:38,456 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:12:38,456 - finmas_enhanced - INFO - Fetching GCP segment data for GOOGL
2025-06-21 15:12:39,901 - finmas_enhanced - INFO - Fetching 4 10-K filings for GOOGL
2025-06-21 15:12:58,310 - finmas_enhanced - INFO - Fetching 4 10-Q filings for GOOGL
2025-06-21 15:13:10,882 - finmas_enhanced - INFO - Retrieved 8 filings for GOOGL
2025-06-21 15:13:10,882 - finmas_enhanced - INFO - Fetching stock data for GOOGL
2025-06-21 15:13:11,112 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/GOOGL/info - SUCCESS (229.97ms)
2025-06-21 15:13:11,112 - finmas_enhanced - INFO - Stock data retrieved for GOOGL: $166.64
2025-06-21 15:13:11,116 - finmas_enhanced - INFO - Fetching financial metrics for GOOGL
2025-06-21 15:13:12,177 - finmas_enhanced - INFO - Financial metrics retrieved for GOOGL
2025-06-21 15:13:24,999 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (617+466=1083 tokens, $0.0088, 12822.52ms)
2025-06-21 15:13:25,002 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 617+466=1083, Cost: $0.0088
2025-06-21 15:13:25,002 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: 'tuple' object has no attribute 'get'
2025-06-21 15:13:25,003 - finmas_enhanced - INFO - Primary agent segment_analysis completed analysis
2025-06-21 15:13:25,003 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:13:25,003 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:13:25,003 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:13:35,135 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (231+355=586 tokens, $0.0060, 10130.59ms)
2025-06-21 15:13:35,135 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 231+355=586, Cost: $0.0060
2025-06-21 15:13:35,135 - finmas_enhanced - INFO - Synthesis completed for GOOGL - Total cost: $0.0060
2025-06-21 15:17:35,945 - finmas_enhanced - INFO - Analyzing query: gcp revenue growth for google for 10k docs...
2025-06-21 15:17:35,945 - finmas_enhanced - INFO - Analyzing query intent: gcp revenue growth for google for 10k docs...
2025-06-21 15:17:43,695 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1457+305=1762 tokens, $0.0089, 7750.08ms) with 1 function calls
2025-06-21 15:17:43,696 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1457+305=1762, Cost: $0.0089
2025-06-21 15:17:43,696 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:17:43,696 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:17:43,696 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:17:44,318 - finmas_enhanced - INFO - Ticker GOOGL validated successfully
2025-06-21 15:17:44,318 - finmas_enhanced - INFO - Validated tickers: ['GOOGL'] -> ['GOOGL']
2025-06-21 15:17:44,318 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: segment_analysis
2025-06-21 15:17:44,318 - finmas_enhanced - INFO - Starting dynamic analysis for GOOGL with primary agent: segment_analysis
2025-06-21 15:17:44,318 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:17:44,318 - finmas_enhanced - INFO - Fetching GCP segment data for GOOGL
2025-06-21 15:17:44,318 - finmas_enhanced - INFO - Fetching 4 10-K filings for GOOGL
2025-06-21 15:18:00,825 - finmas_enhanced - INFO - Fetching 4 10-Q filings for GOOGL
2025-06-21 15:18:11,816 - finmas_enhanced - INFO - Retrieved 8 filings for GOOGL
2025-06-21 15:18:11,816 - finmas_enhanced - INFO - Fetching stock data for GOOGL
2025-06-21 15:18:12,031 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/GOOGL/info - SUCCESS (214.98ms)
2025-06-21 15:18:12,031 - finmas_enhanced - INFO - Stock data retrieved for GOOGL: $166.64
2025-06-21 15:18:12,031 - finmas_enhanced - INFO - Fetching financial metrics for GOOGL
2025-06-21 15:18:12,206 - finmas_enhanced - INFO - Financial metrics retrieved for GOOGL
2025-06-21 15:18:28,777 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (617+633=1250 tokens, $0.0113, 16571.68ms)
2025-06-21 15:18:28,777 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 617+633=1250, Cost: $0.0113
2025-06-21 15:18:28,777 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: 'tuple' object has no attribute 'get'
2025-06-21 15:18:28,777 - finmas_enhanced - INFO - Primary agent segment_analysis completed analysis
2025-06-21 15:18:28,777 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:18:28,777 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:18:28,777 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:18:40,498 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (231+409=640 tokens, $0.0068, 11720.93ms)
2025-06-21 15:18:40,498 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 231+409=640, Cost: $0.0068
2025-06-21 15:18:40,498 - finmas_enhanced - INFO - Synthesis completed for GOOGL - Total cost: $0.0068
2025-06-21 15:19:00,888 - finmas_enhanced - INFO - Cleaning up Financial Analyst resources
2025-06-21 15:19:04,081 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_151904
2025-06-21 15:19:04,085 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_151904
2025-06-21 15:19:04,085 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 15:19:04,232 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 15:19:05,435 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (16+4=20 tokens, $0.0001, 1203.89ms)
2025-06-21 15:19:05,441 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 15:19:05,441 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:19:05,441 - finmas_enhanced - INFO - Agent Coordinator initialized with 11 total agents (6 original + 5 specialized)
2025-06-21 15:19:05,442 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 15:19:07,419 - finmas_enhanced - INFO - Analyzing query: gcp revenue growth for google for 10k docs...
2025-06-21 15:19:07,419 - finmas_enhanced - INFO - Analyzing query intent: gcp revenue growth for google for 10k docs...
2025-06-21 15:19:13,085 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1457+305=1762 tokens, $0.0089, 5659.41ms) with 1 function calls
2025-06-21 15:19:13,085 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1457+305=1762, Cost: $0.0089
2025-06-21 15:19:13,085 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:19:13,085 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:19:13,085 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:19:13,866 - finmas_enhanced - INFO - Ticker GOOGL validated successfully
2025-06-21 15:19:13,866 - finmas_enhanced - INFO - Validated tickers: ['GOOGL'] -> ['GOOGL']
2025-06-21 15:19:13,866 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: segment_analysis
2025-06-21 15:19:13,866 - finmas_enhanced - INFO - Starting dynamic analysis for GOOGL with primary agent: segment_analysis
2025-06-21 15:19:14,535 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:19:14,536 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:19:14,536 - finmas_enhanced - INFO - Fetching GCP segment data for GOOGL
2025-06-21 15:19:16,035 - finmas_enhanced - INFO - Fetching 4 10-K filings for GOOGL
2025-06-21 15:19:36,435 - finmas_enhanced - INFO - Fetching 4 10-Q filings for GOOGL
2025-06-21 15:19:49,275 - finmas_enhanced - INFO - Retrieved 8 filings for GOOGL
2025-06-21 15:19:49,275 - finmas_enhanced - INFO - Fetching stock data for GOOGL
2025-06-21 15:19:49,489 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/GOOGL/info - SUCCESS (212.43ms)
2025-06-21 15:19:49,489 - finmas_enhanced - INFO - Stock data retrieved for GOOGL: $166.64
2025-06-21 15:19:49,489 - finmas_enhanced - INFO - Fetching financial metrics for GOOGL
2025-06-21 15:19:50,341 - finmas_enhanced - INFO - Financial metrics retrieved for GOOGL
2025-06-21 15:20:05,580 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (617+552=1169 tokens, $0.0101, 15238.58ms)
2025-06-21 15:20:05,580 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 617+552=1169, Cost: $0.0101
2025-06-21 15:20:05,581 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: 'tuple' object has no attribute 'get'
2025-06-21 15:20:05,581 - finmas_enhanced - INFO - Primary agent segment_analysis completed analysis
2025-06-21 15:20:05,581 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:20:05,581 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:20:05,581 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:20:17,409 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (231+409=640 tokens, $0.0068, 11824.28ms)
2025-06-21 15:20:17,409 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 231+409=640, Cost: $0.0068
2025-06-21 15:20:17,409 - finmas_enhanced - INFO - Synthesis completed for GOOGL - Total cost: $0.0068
2025-06-21 15:25:25,202 - finmas_enhanced - INFO - Cleaning up Financial Analyst resources
2025-06-21 15:25:30,552 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_152530
2025-06-21 15:25:30,556 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_152530
2025-06-21 15:25:30,557 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 15:25:30,719 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 15:25:31,889 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (16+4=20 tokens, $0.0001, 1169.95ms)
2025-06-21 15:25:31,889 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 15:25:31,889 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:25:31,889 - finmas_enhanced - INFO - Agent Coordinator initialized with 11 total agents (6 original + 5 specialized)
2025-06-21 15:25:31,889 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 15:25:35,368 - finmas_enhanced - INFO - Analyzing query: gcp revenue growth for google for 10k docs...
2025-06-21 15:25:35,373 - finmas_enhanced - INFO - Analyzing query intent: gcp revenue growth for google for 10k docs...
2025-06-21 15:25:41,476 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1457+305=1762 tokens, $0.0089, 6103.52ms) with 1 function calls
2025-06-21 15:25:41,476 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1457+305=1762, Cost: $0.0089
2025-06-21 15:25:41,476 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:25:41,476 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:25:41,476 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:25:42,382 - finmas_enhanced - INFO - Ticker GOOGL validated successfully
2025-06-21 15:25:42,382 - finmas_enhanced - INFO - Validated tickers: ['GOOGL'] -> ['GOOGL']
2025-06-21 15:25:42,382 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: segment_analysis
2025-06-21 15:25:42,382 - finmas_enhanced - INFO - Starting dynamic analysis for GOOGL with primary agent: segment_analysis
2025-06-21 15:25:43,165 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:25:43,165 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:25:43,165 - finmas_enhanced - INFO - Fetching GCP segment data for GOOGL
2025-06-21 15:25:44,616 - finmas_enhanced - INFO - Fetching 4 10-K filings for GOOGL
2025-06-21 15:26:05,589 - finmas_enhanced - INFO - Fetching 4 10-Q filings for GOOGL
2025-06-21 15:26:19,348 - finmas_enhanced - INFO - Retrieved 8 filings for GOOGL
2025-06-21 15:26:19,349 - finmas_enhanced - INFO - Fetching stock data for GOOGL
2025-06-21 15:26:19,560 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/GOOGL/info - SUCCESS (210.41ms)
2025-06-21 15:26:19,560 - finmas_enhanced - INFO - Stock data retrieved for GOOGL: $166.64
2025-06-21 15:26:19,560 - finmas_enhanced - INFO - Fetching financial metrics for GOOGL
2025-06-21 15:26:20,875 - finmas_enhanced - INFO - Financial metrics retrieved for GOOGL
2025-06-21 15:26:36,230 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (617+648=1265 tokens, $0.0116, 15355.92ms)
2025-06-21 15:26:36,230 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 617+648=1265, Cost: $0.0116
2025-06-21 15:26:36,230 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: 'tuple' object has no attribute 'get'
2025-06-21 15:26:36,230 - finmas_enhanced - INFO - Primary agent segment_analysis completed analysis
2025-06-21 15:26:36,230 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:26:36,230 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:26:36,230 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:26:46,677 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (231+422=653 tokens, $0.0070, 10446.70ms)
2025-06-21 15:26:46,677 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 231+422=653, Cost: $0.0070
2025-06-21 15:26:46,677 - finmas_enhanced - INFO - Synthesis completed for GOOGL - Total cost: $0.0070
2025-06-21 15:28:58,903 - finmas_enhanced - INFO - Cleaning up Financial Analyst resources
2025-06-21 15:29:02,899 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_152902
2025-06-21 15:29:02,904 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_152902
2025-06-21 15:29:02,904 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 15:29:03,037 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 15:29:04,162 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (16+4=20 tokens, $0.0001, 1125.22ms)
2025-06-21 15:29:04,162 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 15:29:04,162 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:29:04,162 - finmas_enhanced - INFO - Agent Coordinator initialized with 11 total agents (6 original + 5 specialized)
2025-06-21 15:29:04,162 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 15:29:13,584 - finmas_enhanced - INFO - Analyzing query: gcp revenue growth for google for 10k docs...
2025-06-21 15:29:13,584 - finmas_enhanced - INFO - Analyzing query intent: gcp revenue growth for google for 10k docs...
2025-06-21 15:29:20,290 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1457+287=1744 tokens, $0.0087, 6706.10ms) with 1 function calls
2025-06-21 15:29:20,290 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1457+287=1744, Cost: $0.0087
2025-06-21 15:29:20,290 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:29:20,290 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:29:20,290 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:29:22,384 - finmas_enhanced - INFO - Ticker GOOGL validated successfully
2025-06-21 15:29:22,384 - finmas_enhanced - INFO - Validated tickers: ['GOOGL'] -> ['GOOGL']
2025-06-21 15:29:22,384 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: segment_analysis
2025-06-21 15:29:22,395 - finmas_enhanced - INFO - Starting dynamic analysis for GOOGL with primary agent: segment_analysis
2025-06-21 15:29:22,986 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:29:22,986 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:29:22,986 - finmas_enhanced - INFO - Fetching GCP segment data for GOOGL
2025-06-21 15:29:24,502 - finmas_enhanced - INFO - Fetching 4 10-K filings for GOOGL
2025-06-21 15:29:43,969 - finmas_enhanced - INFO - Fetching 4 10-Q filings for GOOGL
2025-06-21 15:29:57,127 - finmas_enhanced - INFO - Retrieved 8 filings for GOOGL
2025-06-21 15:29:57,127 - finmas_enhanced - INFO - Fetching stock data for GOOGL
2025-06-21 15:29:57,360 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/GOOGL/info - SUCCESS (232.56ms)
2025-06-21 15:29:57,361 - finmas_enhanced - INFO - Stock data retrieved for GOOGL: $166.64
2025-06-21 15:29:57,361 - finmas_enhanced - INFO - Fetching financial metrics for GOOGL
2025-06-21 15:29:58,206 - finmas_enhanced - INFO - Financial metrics retrieved for GOOGL
2025-06-21 15:30:19,740 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (617+657=1274 tokens, $0.0117, 21530.07ms)
2025-06-21 15:30:19,740 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 617+657=1274, Cost: $0.0117
2025-06-21 15:30:19,745 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: 'tuple' object has no attribute 'get'
2025-06-21 15:30:19,755 - finmas_enhanced - INFO - Primary agent segment_analysis completed analysis
2025-06-21 15:30:19,763 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:30:19,769 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:30:19,771 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:30:33,495 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (231+403=634 tokens, $0.0067, 13715.56ms)
2025-06-21 15:30:33,495 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 231+403=634, Cost: $0.0067
2025-06-21 15:30:33,495 - finmas_enhanced - INFO - Synthesis completed for GOOGL - Total cost: $0.0067
2025-06-21 15:35:25,168 - finmas_enhanced - INFO - Cleaning up Financial Analyst resources
2025-06-21 15:35:28,948 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_153528
2025-06-21 15:35:28,952 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_153528
2025-06-21 15:35:28,953 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 15:35:29,099 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 15:35:31,020 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (16+4=20 tokens, $0.0001, 1921.15ms)
2025-06-21 15:35:31,020 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 15:35:31,020 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:35:31,020 - finmas_enhanced - INFO - Agent Coordinator initialized with 11 total agents (6 original + 5 specialized)
2025-06-21 15:35:31,020 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 15:35:33,069 - finmas_enhanced - INFO - Analyzing query: gcp revenue growth for google for 10k docs...
2025-06-21 15:35:33,069 - finmas_enhanced - INFO - Analyzing query intent: gcp revenue growth for google for 10k docs...
2025-06-21 15:35:39,768 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1457+305=1762 tokens, $0.0089, 6699.75ms) with 1 function calls
2025-06-21 15:35:39,768 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1457+305=1762, Cost: $0.0089
2025-06-21 15:35:39,768 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:35:39,768 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:35:39,768 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:35:40,456 - finmas_enhanced - INFO - Ticker GOOGL validated successfully
2025-06-21 15:35:40,456 - finmas_enhanced - INFO - Validated tickers: ['GOOGL'] -> ['GOOGL']
2025-06-21 15:35:40,456 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: segment_analysis
2025-06-21 15:35:40,456 - finmas_enhanced - INFO - Starting dynamic analysis for GOOGL with primary agent: segment_analysis
2025-06-21 15:35:41,010 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:35:41,010 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:35:41,010 - finmas_enhanced - INFO - Fetching GCP segment data for GOOGL
2025-06-21 15:35:42,795 - finmas_enhanced - INFO - Fetching 4 10-K filings for GOOGL
2025-06-21 15:36:01,295 - finmas_enhanced - INFO - Fetching 4 10-Q filings for GOOGL
2025-06-21 15:36:13,946 - finmas_enhanced - INFO - Retrieved 8 filings for GOOGL
2025-06-21 15:36:13,947 - finmas_enhanced - INFO - Fetching stock data for GOOGL
2025-06-21 15:36:14,152 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/GOOGL/info - SUCCESS (205.52ms)
2025-06-21 15:36:14,152 - finmas_enhanced - INFO - Stock data retrieved for GOOGL: $166.64
2025-06-21 15:36:14,152 - finmas_enhanced - INFO - Fetching financial metrics for GOOGL
2025-06-21 15:36:15,012 - finmas_enhanced - INFO - Financial metrics retrieved for GOOGL
2025-06-21 15:36:29,360 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (617+524=1141 tokens, $0.0097, 14347.70ms)
2025-06-21 15:36:29,360 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 617+524=1141, Cost: $0.0097
2025-06-21 15:36:29,360 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: 'tuple' object has no attribute 'get'
2025-06-21 15:36:29,360 - finmas_enhanced - INFO - Primary agent segment_analysis completed analysis
2025-06-21 15:36:29,360 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:36:29,360 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:36:29,362 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:36:39,385 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (231+339=570 tokens, $0.0058, 10023.14ms)
2025-06-21 15:36:39,385 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 231+339=570, Cost: $0.0058
2025-06-21 15:36:39,385 - finmas_enhanced - INFO - Synthesis completed for GOOGL - Total cost: $0.0058
2025-06-21 15:39:11,162 - finmas_enhanced - INFO - Cleaning up Financial Analyst resources
2025-06-21 15:39:13,955 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_153913
2025-06-21 15:39:13,959 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_153913
2025-06-21 15:39:13,962 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 15:39:14,095 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 15:39:16,029 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (16+4=20 tokens, $0.0001, 1931.71ms)
2025-06-21 15:39:16,029 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 15:39:16,029 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:39:16,029 - finmas_enhanced - INFO - Agent Coordinator initialized with 11 total agents (6 original + 5 specialized)
2025-06-21 15:39:16,029 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 15:39:17,549 - finmas_enhanced - INFO - Analyzing query: gcp revenue growth for google for 10k docs...
2025-06-21 15:39:17,549 - finmas_enhanced - INFO - Analyzing query intent: gcp revenue growth for google for 10k docs...
2025-06-21 15:39:24,300 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1457+316=1773 tokens, $0.0091, 6751.13ms) with 1 function calls
2025-06-21 15:39:24,300 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1457+316=1773, Cost: $0.0091
2025-06-21 15:39:24,300 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:39:24,300 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:39:24,300 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:39:25,253 - finmas_enhanced - INFO - Ticker GOOGL validated successfully
2025-06-21 15:39:25,254 - finmas_enhanced - INFO - Validated tickers: ['GOOGL'] -> ['GOOGL']
2025-06-21 15:39:25,254 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: segment_analysis
2025-06-21 15:39:25,254 - finmas_enhanced - INFO - Starting dynamic analysis for GOOGL with primary agent: segment_analysis
2025-06-21 15:39:25,824 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:39:25,824 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:39:25,824 - finmas_enhanced - INFO - Fetching GCP segment data for GOOGL
2025-06-21 15:39:27,085 - finmas_enhanced - INFO - Fetching 4 10-K filings for GOOGL
2025-06-21 15:39:47,755 - finmas_enhanced - INFO - Fetching 4 10-Q filings for GOOGL
2025-06-21 15:39:59,425 - finmas_enhanced - INFO - Retrieved 8 filings for GOOGL
2025-06-21 15:39:59,426 - finmas_enhanced - INFO - Fetching stock data for GOOGL
2025-06-21 15:39:59,645 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/GOOGL/info - SUCCESS (218.90ms)
2025-06-21 15:39:59,645 - finmas_enhanced - INFO - Stock data retrieved for GOOGL: $166.64
2025-06-21 15:39:59,645 - finmas_enhanced - INFO - Fetching financial metrics for GOOGL
2025-06-21 15:40:00,577 - finmas_enhanced - INFO - Financial metrics retrieved for GOOGL
2025-06-21 15:40:15,709 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (617+556=1173 tokens, $0.0102, 15131.92ms)
2025-06-21 15:40:15,709 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 617+556=1173, Cost: $0.0102
2025-06-21 15:40:15,709 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: 'tuple' object has no attribute 'get'
2025-06-21 15:40:15,709 - finmas_enhanced - INFO - Primary agent segment_analysis completed analysis
2025-06-21 15:40:15,709 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:40:15,709 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:40:15,709 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:40:24,690 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (231+397=628 tokens, $0.0066, 8980.58ms)
2025-06-21 15:40:24,690 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 231+397=628, Cost: $0.0066
2025-06-21 15:40:24,690 - finmas_enhanced - INFO - Synthesis completed for GOOGL - Total cost: $0.0066
2025-06-21 15:41:07,839 - finmas_enhanced - INFO - Analyzing query: uv run .\cli.py --help...
2025-06-21 15:41:07,839 - finmas_enhanced - INFO - Analyzing query intent: uv run .\cli.py --help...
2025-06-21 15:41:13,319 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1456+143=1599 tokens, $0.0065, 5480.15ms)
2025-06-21 15:41:13,319 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1456+143=1599, Cost: $0.0065
2025-06-21 15:41:13,319 - finmas_enhanced - WARNING - No function calls received, using fallback analysis
2025-06-21 15:41:13,319 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['UV', 'RUN', 'CLI', 'PY', 'HELP'], confidence: 0.7
2025-06-21 15:41:13,319 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:41:13,650 - finmas_enhanced - WARNING - Ticker UV validation failed
2025-06-21 15:41:13,969 - finmas_enhanced - INFO - Ticker RUN validated successfully
2025-06-21 15:41:14,279 - finmas_enhanced - WARNING - Ticker CLI validation failed
2025-06-21 15:41:14,596 - finmas_enhanced - INFO - Ticker PY validated successfully
2025-06-21 15:41:14,906 - finmas_enhanced - WARNING - Ticker HELP validation failed
2025-06-21 15:41:14,906 - finmas_enhanced - INFO - Validated tickers: ['UV', 'RUN', 'CLI', 'PY', 'HELP'] -> ['RUN', 'PY']
2025-06-21 15:41:14,908 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: technical
2025-06-21 15:41:14,908 - finmas_enhanced - INFO - Starting dynamic analysis for RUN with primary agent: technical
2025-06-21 15:41:14,908 - finmas_enhanced - INFO - Technical Analyst analyzing RUN
2025-06-21 15:41:14,908 - finmas_enhanced - INFO - Fetching stock data for RUN
2025-06-21 15:41:15,085 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/RUN/info - SUCCESS (177.73ms)
2025-06-21 15:41:15,085 - finmas_enhanced - INFO - Stock data retrieved for RUN: $6.23
2025-06-21 15:41:15,085 - finmas_enhanced - INFO - Calculating technical indicators for RUN
2025-06-21 15:41:15,696 - finmas_enhanced - INFO - Technical indicators calculated for RUN
2025-06-21 15:41:37,806 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (590+769=1359 tokens, $0.0133, 22109.43ms)
2025-06-21 15:41:37,806 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 590+769=1359, Cost: $0.0133
2025-06-21 15:41:37,806 - finmas_enhanced - INFO - Primary agent technical completed analysis
2025-06-21 15:41:37,806 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:41:37,806 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:41:37,808 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:41:57,224 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (930+854=1784 tokens, $0.0156, 19416.17ms)
2025-06-21 15:41:57,226 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 930+854=1784, Cost: $0.0156
2025-06-21 15:41:57,226 - finmas_enhanced - INFO - Synthesis completed for RUN - Total cost: $0.0289
2025-06-21 15:43:35,994 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_154335
2025-06-21 15:43:36,007 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_154336
2025-06-21 15:43:36,007 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 15:43:36,160 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 15:43:37,088 - finmas_enhanced - WARNING - Bedrock API call attempt 1 failed: An error occurred (UnrecognizedClientException) when calling the Converse operation: The security token included in the request is invalid.
2025-06-21 15:43:38,255 - finmas_enhanced - WARNING - Bedrock API call attempt 2 failed: An error occurred (UnrecognizedClientException) when calling the Converse operation: The security token included in the request is invalid.
2025-06-21 15:43:40,421 - finmas_enhanced - WARNING - Bedrock API call attempt 3 failed: An error occurred (UnrecognizedClientException) when calling the Converse operation: The security token included in the request is invalid.
2025-06-21 15:43:40,421 - finmas_enhanced - ERROR - Bedrock health check failed: An error occurred (UnrecognizedClientException) when calling the Converse operation: The security token included in the request is invalid.
2025-06-21 15:43:40,421 - finmas_enhanced - ERROR - Initialization failed: Bedrock health check failed
2025-06-21 15:44:49,011 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_154449
2025-06-21 15:44:49,013 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_154449
2025-06-21 15:44:49,013 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:44:49,020 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:44:49,020 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: object Mock can't be used in 'await' expression
2025-06-21 15:44:49,020 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:44:49,020 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: object Mock can't be used in 'await' expression
2025-06-21 15:44:49,024 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:44:49,024 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: object Mock can't be used in 'await' expression
2025-06-21 15:44:49,027 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:44:49,027 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: object Mock can't be used in 'await' expression
2025-06-21 15:45:33,234 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_154533
2025-06-21 15:45:33,240 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_154533
2025-06-21 15:45:33,240 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:45:33,240 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'tuple'>: ('GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}])
2025-06-21 15:45:33,240 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'tuple'>: ('GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}])
2025-06-21 15:45:33,240 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'tuple'>: ('GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}])
2025-06-21 15:45:33,240 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'list'>: ['GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}]]
2025-06-21 15:45:33,240 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'list'>: ['GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}]]
2025-06-21 15:45:33,240 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'list'>: ['GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}]]
2025-06-21 15:45:33,240 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:49:10,044 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_154910
2025-06-21 15:49:10,045 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_154910
2025-06-21 15:49:10,045 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:49:10,048 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:49:10,054 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'tuple'>: ('GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}])
2025-06-21 15:49:10,055 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'tuple'>: ('GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}])
2025-06-21 15:49:10,055 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'tuple'>: ('GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}])
2025-06-21 15:49:10,056 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'list'>: ['GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}]]
2025-06-21 15:49:10,056 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'list'>: ['GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}]]
2025-06-21 15:49:10,056 - finmas_enhanced - ERROR - Expected dict for segment_data, got <class 'list'>: ['GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}]]
2025-06-21 15:49:10,057 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:49:52,485 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_154952
2025-06-21 15:49:52,495 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_154952
2025-06-21 15:49:52,495 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:49:52,499 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:49:52,500 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: 'coroutine' object has no attribute 'get'
2025-06-21 15:49:52,606 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:49:52,623 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: Unexpected segment_data type: <class 'tuple'> - ('GOOGL', 'GCP', [{'name': 'Google Cloud', 'revenue': **********}])
2025-06-21 15:49:52,625 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:50:08,706 - finmas_enhanced - INFO - Cleaning up Financial Analyst resources
2025-06-21 15:50:11,324 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_155011
2025-06-21 15:50:11,328 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_155011
2025-06-21 15:50:11,328 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 15:50:11,469 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 15:50:12,822 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (16+4=20 tokens, $0.0001, 1351.93ms)
2025-06-21 15:50:12,823 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 15:50:12,823 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:50:12,823 - finmas_enhanced - INFO - Agent Coordinator initialized with 11 total agents (6 original + 5 specialized)
2025-06-21 15:50:12,823 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 15:50:17,834 - finmas_enhanced - INFO - Analyzing query: gcp revenue growth for google for 10k docs...
2025-06-21 15:50:17,834 - finmas_enhanced - INFO - Analyzing query intent: gcp revenue growth for google for 10k docs...
2025-06-21 15:50:24,368 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1457+309=1766 tokens, $0.0090, 6532.04ms) with 1 function calls
2025-06-21 15:50:24,368 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1457+309=1766, Cost: $0.0090
2025-06-21 15:50:24,368 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:50:24,368 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 15:50:24,368 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:50:25,199 - finmas_enhanced - INFO - Ticker GOOGL validated successfully
2025-06-21 15:50:25,199 - finmas_enhanced - INFO - Validated tickers: ['GOOGL'] -> ['GOOGL']
2025-06-21 15:50:25,199 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: segment_analysis
2025-06-21 15:50:25,199 - finmas_enhanced - INFO - Starting dynamic analysis for GOOGL with primary agent: segment_analysis
2025-06-21 15:50:25,902 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 15:50:25,903 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 15:50:25,903 - finmas_enhanced - INFO - Fetching GCP segment data for GOOGL
2025-06-21 15:50:27,874 - finmas_enhanced - INFO - Fetching 4 10-K filings for GOOGL
2025-06-21 15:50:50,049 - finmas_enhanced - INFO - Fetching 4 10-Q filings for GOOGL
2025-06-21 15:51:02,899 - finmas_enhanced - INFO - Retrieved 8 filings for GOOGL
2025-06-21 15:51:02,899 - finmas_enhanced - INFO - Fetching stock data for GOOGL
2025-06-21 15:51:03,148 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/GOOGL/info - SUCCESS (249.75ms)
2025-06-21 15:51:03,148 - finmas_enhanced - INFO - Stock data retrieved for GOOGL: $166.64
2025-06-21 15:51:03,148 - finmas_enhanced - INFO - Fetching financial metrics for GOOGL
2025-06-21 15:51:04,101 - finmas_enhanced - INFO - Financial metrics retrieved for GOOGL
2025-06-21 15:51:24,219 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (617+612=1229 tokens, $0.0110, 20117.45ms)
2025-06-21 15:51:24,219 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 617+612=1229, Cost: $0.0110
2025-06-21 15:51:24,220 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: 'tuple' object has no attribute 'get'
2025-06-21 15:51:24,220 - finmas_enhanced - INFO - Primary agent segment_analysis completed analysis
2025-06-21 15:51:24,220 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:51:24,220 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:51:24,221 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:51:38,137 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (231+433=664 tokens, $0.0072, 13915.89ms)
2025-06-21 15:51:38,137 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 231+433=664, Cost: $0.0072
2025-06-21 15:51:38,137 - finmas_enhanced - INFO - Synthesis completed for GOOGL - Total cost: $0.0072
2025-06-21 15:52:23,576 - finmas_enhanced - INFO - Analyzing query: Is R&D spend rising as % of revenue? for nike...
2025-06-21 15:52:23,576 - finmas_enhanced - INFO - Analyzing query intent: Is R&D spend rising as % of revenue? for nike...
2025-06-21 15:52:30,010 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1459+276=1735 tokens, $0.0085, 6433.50ms) with 1 function calls
2025-06-21 15:52:30,010 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1459+276=1735, Cost: $0.0085
2025-06-21 15:52:30,010 - finmas_enhanced - INFO - Intent analysis complete: fundamental, tickers: ['NKE'], confidence: 0.95
2025-06-21 15:52:30,010 - finmas_enhanced - INFO - Intent analysis: fundamental, tickers: ['NKE'], confidence: 0.95
2025-06-21 15:52:30,013 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 15:52:34,729 - finmas_enhanced - INFO - Ticker NKE validated successfully
2025-06-21 15:52:34,729 - finmas_enhanced - INFO - Validated tickers: ['NKE'] -> ['NKE']
2025-06-21 15:52:34,729 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: fundamental
2025-06-21 15:52:34,729 - finmas_enhanced - INFO - Starting dynamic analysis for NKE with primary agent: fundamental
2025-06-21 15:52:34,729 - finmas_enhanced - INFO - Fundamental Analyst analyzing NKE
2025-06-21 15:52:34,729 - finmas_enhanced - INFO - Fetching stock data for NKE
2025-06-21 15:52:34,874 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/NKE/info - SUCCESS (144.81ms)
2025-06-21 15:52:34,875 - finmas_enhanced - INFO - Stock data retrieved for NKE: $59.79
2025-06-21 15:52:34,875 - finmas_enhanced - INFO - Fetching financial metrics for NKE
2025-06-21 15:52:35,874 - finmas_enhanced - INFO - Financial metrics retrieved for NKE
2025-06-21 15:52:55,276 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (483+779=1262 tokens, $0.0131, 19402.33ms)
2025-06-21 15:52:55,276 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 483+779=1262, Cost: $0.0131
2025-06-21 15:52:55,276 - finmas_enhanced - INFO - Primary agent fundamental completed analysis
2025-06-21 15:52:55,278 - finmas_enhanced - INFO - Communication round 1
2025-06-21 15:52:55,278 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 15:52:55,278 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 15:53:21,262 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (941+888=1829 tokens, $0.0161, 25984.53ms)
2025-06-21 15:53:21,262 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 941+888=1829, Cost: $0.0161
2025-06-21 15:53:21,262 - finmas_enhanced - INFO - Synthesis completed for NKE - Total cost: $0.0293
2025-06-21 16:10:39,276 - finmas_enhanced - INFO - Cleaning up Financial Analyst resources
2025-06-21 16:10:42,315 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_161042
2025-06-21 16:10:42,320 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_161042
2025-06-21 16:10:42,321 - finmas_enhanced - INFO - Financial Analyst initialized
2025-06-21 16:10:42,466 - finmas_enhanced - INFO - Bedrock client initialized with model: us.anthropic.claude-3-5-sonnet-20241022-v2:0
2025-06-21 16:10:44,562 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (16+4=20 tokens, $0.0001, 2096.48ms)
2025-06-21 16:10:44,562 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 16+4=20, Cost: $0.0001
2025-06-21 16:10:44,562 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 16:10:44,562 - finmas_enhanced - INFO - Agent Coordinator initialized with 11 total agents (6 original + 5 specialized)
2025-06-21 16:10:44,562 - finmas_enhanced - INFO - All components initialized successfully
2025-06-21 16:10:46,211 - finmas_enhanced - INFO - Analyzing query: gcp revenue growth for google for 10k docs...
2025-06-21 16:10:46,211 - finmas_enhanced - INFO - Analyzing query intent: gcp revenue growth for google for 10k docs...
2025-06-21 16:10:53,629 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (1457+305=1762 tokens, $0.0089, 7414.74ms) with 1 function calls
2025-06-21 16:10:53,629 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 1457+305=1762, Cost: $0.0089
2025-06-21 16:10:53,629 - finmas_enhanced - INFO - Intent analysis complete: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 16:10:53,629 - finmas_enhanced - INFO - Intent analysis: analyze, tickers: ['GOOGL'], confidence: 0.95
2025-06-21 16:10:53,629 - finmas_enhanced - INFO - Financial data provider initialized
2025-06-21 16:10:54,365 - finmas_enhanced - INFO - Ticker GOOGL validated successfully
2025-06-21 16:10:54,365 - finmas_enhanced - INFO - Validated tickers: ['GOOGL'] -> ['GOOGL']
2025-06-21 16:10:54,365 - finmas_enhanced - INFO - Smart orchestration starting with primary agent: segment_analysis
2025-06-21 16:10:54,365 - finmas_enhanced - INFO - Starting dynamic analysis for GOOGL with primary agent: segment_analysis
2025-06-21 16:10:55,121 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 16:10:55,122 - finmas_enhanced - INFO - Segment Analyst analyzing GCP for GOOGL
2025-06-21 16:10:55,122 - finmas_enhanced - INFO - Fetching GCP segment data for GOOGL
2025-06-21 16:10:55,122 - finmas_enhanced - INFO - Fetching period None segment data for GOOGL
2025-06-21 16:10:57,005 - finmas_enhanced - INFO - Fetching 4 10-K filings for GOOGL
2025-06-21 16:11:32,849 - finmas_enhanced - INFO - Fetching 4 10-Q filings for GOOGL
2025-06-21 16:11:45,863 - finmas_enhanced - INFO - Retrieved 8 filings for GOOGL
2025-06-21 16:11:45,863 - finmas_enhanced - INFO - Fetching stock data for GOOGL
2025-06-21 16:11:46,091 - finmas_enhanced - INFO - API Call [yfinance] GET ticker/GOOGL/info - SUCCESS (227.83ms)
2025-06-21 16:11:46,113 - finmas_enhanced - INFO - Stock data retrieved for GOOGL: $166.64
2025-06-21 16:11:46,116 - finmas_enhanced - INFO - Fetching financial metrics for GOOGL
2025-06-21 16:11:46,957 - finmas_enhanced - INFO - Financial metrics retrieved for GOOGL
2025-06-21 16:12:05,371 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (617+609=1226 tokens, $0.0110, 18410.62ms)
2025-06-21 16:12:05,371 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 617+609=1226, Cost: $0.0110
2025-06-21 16:12:05,371 - finmas_enhanced - ERROR - Segment analysis failed for GOOGL GCP: 'tuple' object has no attribute 'get'
2025-06-21 16:12:05,372 - finmas_enhanced - INFO - Primary agent segment_analysis completed analysis
2025-06-21 16:12:05,372 - finmas_enhanced - INFO - Communication round 1
2025-06-21 16:12:05,372 - finmas_enhanced - INFO - No additional agents needed
2025-06-21 16:12:05,372 - finmas_enhanced - INFO - Dynamic analysis complete with 1 agents
2025-06-21 16:12:18,857 - finmas_enhanced - INFO - AI Call us.anthropic.claude-3-5-sonnet-20241022-v2:0 - SUCCESS (231+441=672 tokens, $0.0073, 13485.28ms)
2025-06-21 16:12:18,857 - finmas_enhanced - INFO - Bedrock API call successful - Tokens: 231+441=672, Cost: $0.0073
2025-06-21 16:12:18,858 - finmas_enhanced - INFO - Synthesis completed for GOOGL - Total cost: $0.0073
2025-06-21 16:13:05,722 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_161305
2025-06-21 16:13:05,724 - finmas_enhanced - INFO - FinMAS Enhanced session started: 20250621_161305
2025-06-21 16:13:05,724 - finmas_enhanced - INFO - Enhanced SEC data provider initialized
2025-06-21 16:13:05,724 - finmas_enhanced - INFO - Fetching latest 10-K filing for INVALID_TICKER_XYZ123
2025-06-21 16:13:08,220 - finmas_enhanced - WARNING - No 10-K filings found for INVALID_TICKER_XYZ123
2025-06-21 16:13:08,220 - finmas_enhanced - ERROR - [_extract_financial_statements] Error extracting financial statements: expected string or bytes-like object, got 'Mock' | Function: _extract_financial_statements | Description: Error occurred while extracting financial statement data from 10-K/10-Q filings using pattern matching
2025-06-21 16:13:08,220 - finmas_enhanced - ERROR - [_extract_segment_data] Error extracting segment data: expected string or bytes-like object, got 'Mock' | Function: _extract_segment_data | Description: Error occurred while extracting comprehensive business segment data with enhanced cloud detection from SEC filing text
2025-06-21 16:13:08,225 - finmas_enhanced - ERROR - [_extract_risk_factors] Error extracting risk factors: expected string or bytes-like object, got 'Mock' | Function: _extract_risk_factors | Description: Error occurred while extracting risk factors from SEC filing using pattern matching
2025-06-21 16:13:08,225 - finmas_enhanced - ERROR - [_extract_management_discussion] Error extracting MD&A: expected string or bytes-like object, got 'Mock' | Function: _extract_management_discussion | Description: Error occurred while extracting Management Discussion and Analysis section from SEC filing
2025-06-21 16:13:08,226 - finmas_enhanced - INFO - Fetching GCP segment data for INVALID_TICKER_XYZ123
2025-06-21 16:13:08,226 - finmas_enhanced - INFO - Fetching period None segment data for INVALID_TICKER_XYZ123
2025-06-21 16:13:08,226 - finmas_enhanced - INFO - Fetching 4 10-K filings for INVALID_TICKER_XYZ123
2025-06-21 16:13:08,227 - finmas_enhanced - ERROR - [get_multiple_filings] Failed to get multiple filings for INVALID_TICKER_XYZ123: 'NoneType' object is not iterable | Function: get_multiple_filings | Description: Error occurred while fetching multiple recent SEC filings for comprehensive analysis
2025-06-21 16:13:08,229 - finmas_enhanced - WARNING - [_extract_filing_data] _extract_segment_data returned non-dict: <class 'tuple'> - ('invalid', 'tuple', 'data') | Function: _extract_filing_data | Description: segment_data should be a dictionary but received different type
