"""
Business Segment Analysis Agent

This module implements a specialized agent for analyzing business segments
like Azure, AWS, Office 365, Gaming, etc. with temporal filtering capabilities.
"""

import asyncio
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import re

from .agents import BaseAgent, AgentResult
from .data_sources import FinancialDataProvider
from .sec_data_provider import EnhancedSECDataProvider
from .bedrock_client import BedrockClient
from config import Config
from utils.logging_utils import get_logger


@dataclass
class SegmentAnalysisResult:
    """Result from segment analysis"""
    segment_name: str
    ticker: str
    revenue_data: Dict[str, Any]
    growth_analysis: Dict[str, Any]
    competitive_position: Dict[str, Any]
    future_guidance: Dict[str, Any]
    key_metrics: Dict[str, Any]
    recommendations: List[str]
    confidence: float
    timestamp: str


class SegmentAnalysisAgent(BaseAgent):
    """Agent specialized in business segment analysis with temporal capabilities"""
    
    def __init__(self, bedrock_client: BedrockClient, data_provider: FinancialDataProvider, config: Config):
        super().__init__("Business Segment Analyst", bedrock_client, data_provider)
        self.sec_provider = EnhancedSECDataProvider(config)
        self.config = config
        
    def get_system_prompt(self) -> str:
        return """You are a senior business segment analyst with expertise in analyzing specific business units and product lines. You specialize in:

- Business segment revenue analysis and growth trends
- Competitive positioning within specific market segments
- Segment-specific operational metrics and KPIs
- Future guidance and management targets for segments
- Cross-segment performance comparison
- Market share analysis within segments
- Segment profitability and margin analysis

Your analysis should be:
- Segment-focused with specific revenue data and growth rates
- Include temporal analysis showing trends over time
- Provide competitive context within the segment
- Extract management guidance and future projections
- Identify segment-specific risks and opportunities
- Include source attribution from SEC filings

Always provide specific numbers, growth rates, and quantitative assessments for segments. Be analytical and precise with segment-specific insights."""

    async def analyze_segment(
        self, 
        ticker: str, 
        segment_name: str, 
        period: str = None,
        context: Optional[Dict[str, Any]] = None
    ) -> SegmentAnalysisResult:
        """Perform comprehensive segment analysis"""
        import time
        start_time = time.time()
        
        try:
            self.logger.logger.info(f"Segment Analyst analyzing {segment_name} for {ticker}")
            
            # Get segment-specific data
            segment_data = await self.sec_provider.get_segment_data(ticker, segment_name, period)
            self.logger.logger.debug(f"Received segment_data type: {type(segment_data)}, content: {segment_data}")

            # Get future guidance if period is future year
            future_guidance = {}
            if period and period.isdigit() and int(period) > datetime.now().year:
                future_guidance = await self.sec_provider.get_future_period_analysis(ticker, period, segment_name)

            # Get general financial context
            stock_data = await self.data_provider.get_stock_data(ticker)
            financial_metrics = await self.data_provider.get_financial_metrics(ticker)

            if isinstance(segment_data, dict) and "error" in segment_data:
                raise ValueError(f"Unable to get segment data: {segment_data['error']}")
            elif not isinstance(segment_data, dict):
                raise ValueError(f"Unexpected segment_data type: {type(segment_data)} - {segment_data}")
            
            # Prepare comprehensive analysis prompt
            analysis_prompt = f"""Perform comprehensive business segment analysis for {segment_name} at {ticker}:

SEGMENT REVENUE DATA:
{json.dumps(segment_data, indent=2)}

FUTURE GUIDANCE DATA:
{json.dumps(future_guidance, indent=2)}

COMPANY CONTEXT:
- Current Stock Price: ${stock_data.current_price if stock_data else 'N/A'}
- Market Cap: ${stock_data.market_cap if stock_data else 'N/A'}
- Total Revenue (TTM): ${financial_metrics.revenue_ttm if financial_metrics else 'N/A'}

ANALYSIS REQUIREMENTS:
1. Segment Revenue Analysis:
   - Historical revenue trends and growth rates
   - Segment's contribution to total company revenue
   - Quarter-over-quarter and year-over-year growth
   - Revenue seasonality patterns

2. Growth Analysis:
   - Growth drivers and catalysts
   - Market expansion opportunities
   - Customer adoption trends
   - Competitive dynamics impact

3. Competitive Positioning:
   - Market share within segment
   - Competitive advantages and moats
   - Pricing power and margin trends
   - Innovation and product differentiation

4. Future Outlook:
   - Management guidance and targets
   - Growth projections and assumptions
   - Investment priorities and CapEx allocation
   - Strategic initiatives and roadmap

5. Key Metrics & KPIs:
   - Segment-specific operational metrics
   - Customer metrics (if available)
   - Unit economics and profitability
   - Margin analysis and trends

6. Investment Implications:
   - Segment's impact on overall valuation
   - Risk factors and challenges
   - Catalysts for acceleration/deceleration
   - Recommendations for investors

Provide specific numbers, percentages, and quantitative analysis. Include source attribution with filing dates.
Focus specifically on {segment_name} segment performance and outlook."""

            # Get AI analysis
            analysis_result = await self.bedrock_client.converse_async(
                messages=[{
                    "role": "user",
                    "content": [{"text": analysis_prompt}]
                }],
                system_message=self.get_system_prompt()
            )
            
            # Extract analysis text
            analysis_text = ""
            output_message = analysis_result.get('output', {}).get('message', {})
            content = output_message.get('content', [])
            
            for content_block in content:
                if 'text' in content_block:
                    analysis_text += content_block['text']
            
            # Extract key metrics and recommendations
            key_metrics = self._extract_segment_metrics(segment_data, future_guidance)
            recommendations = self._extract_segment_recommendations(analysis_text, segment_data)
            
            execution_time = time.time() - start_time
            
            return SegmentAnalysisResult(
                segment_name=segment_name,
                ticker=ticker,
                revenue_data=segment_data,
                growth_analysis=self._analyze_growth_trends(segment_data),
                competitive_position=self._analyze_competitive_position(segment_name, analysis_text),
                future_guidance=future_guidance,
                key_metrics=key_metrics,
                recommendations=recommendations,
                confidence=0.85,  # High confidence for segment analysis
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            self.logger.logger.error(f"Segment analysis failed for {ticker} {segment_name}: {str(e)}")
            return SegmentAnalysisResult(
                segment_name=segment_name,
                ticker=ticker,
                revenue_data={},
                growth_analysis={},
                competitive_position={},
                future_guidance={},
                key_metrics={},
                recommendations=[f"Analysis failed: {str(e)}"],
                confidence=0.0,
                timestamp=datetime.now().isoformat()
            )

    async def analyze(self, ticker: str, context: Optional[Dict[str, Any]] = None) -> AgentResult:
        """Standard analyze method for agent compatibility"""
        # Extract segment info from context
        segment_name = context.get('segment_name', 'Azure') if context else 'Azure'
        period = context.get('period') if context else None
        
        segment_result = await self.analyze_segment(ticker, segment_name, period, context)
        
        # Convert to standard AgentResult format
        analysis_text = f"""BUSINESS SEGMENT ANALYSIS: {segment_result.segment_name}

REVENUE PERFORMANCE:
{json.dumps(segment_result.revenue_data, indent=2)}

GROWTH ANALYSIS:
{json.dumps(segment_result.growth_analysis, indent=2)}

COMPETITIVE POSITION:
{json.dumps(segment_result.competitive_position, indent=2)}

FUTURE GUIDANCE:
{json.dumps(segment_result.future_guidance, indent=2)}

KEY RECOMMENDATIONS:
{chr(10).join(f"• {rec}" for rec in segment_result.recommendations)}
"""
        
        return AgentResult(
            agent_name=self.name,
            ticker=ticker,
            analysis=analysis_text,
            confidence=segment_result.confidence,
            key_metrics=segment_result.key_metrics,
            recommendations=segment_result.recommendations,
            timestamp=segment_result.timestamp,
            execution_time_ms=1000.0,  # Placeholder in milliseconds
            cost=0.0  # Placeholder cost
        )

    def _extract_segment_metrics(self, segment_data: Dict[str, Any], future_guidance: Dict[str, Any]) -> Dict[str, Any]:
        """Extract key segment metrics"""
        metrics = {}

        # Defensive check for segment_data type
        if not isinstance(segment_data, dict):
            self.logger.logger.error(f"Expected dict for segment_data, got {type(segment_data)}: {segment_data}")
            return metrics

        # Extract revenue metrics
        if segment_data.get('segment_data'):
            latest_data = segment_data['segment_data'][0] if segment_data['segment_data'] else {}
            if isinstance(latest_data, dict):
                metrics['latest_revenue'] = latest_data.get('revenue')
                metrics['filing_date'] = latest_data.get('filing_date')

        # Defensive check for future_guidance type
        if not isinstance(future_guidance, dict):
            self.logger.logger.error(f"Expected dict for future_guidance, got {type(future_guidance)}: {future_guidance}")
            return metrics

        # Extract growth metrics from guidance
        if future_guidance.get('guidance_data'):
            for date, guidance in future_guidance['guidance_data'].items():
                if isinstance(guidance, dict):
                    if guidance.get('2025_growth_rate'):
                        metrics['2025_growth_target'] = guidance['2025_growth_rate']
                    if guidance.get('2025_revenue_target'):
                        metrics['2025_revenue_target'] = guidance['2025_revenue_target']

        return metrics

    def _extract_segment_recommendations(self, analysis_text: str, segment_data: Dict[str, Any]) -> List[str]:
        """Extract actionable recommendations"""
        recommendations = []

        # Defensive check for segment_data type
        if not isinstance(segment_data, dict):
            self.logger.logger.error(f"Expected dict for segment_data, got {type(segment_data)}: {segment_data}")
            recommendations.append("Unable to analyze segment data due to data format issues")
            return recommendations

        # Basic recommendations based on data availability
        if segment_data.get('segment_data'):
            recommendations.append("Monitor quarterly segment revenue trends")
            recommendations.append("Track competitive positioning within segment")

        if segment_data.get('guidance'):
            recommendations.append("Follow management guidance execution")

        # Add default recommendation
        if not recommendations:
            recommendations.append("Requires deeper segment analysis with more data")

        return recommendations

    def _analyze_growth_trends(self, segment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze growth trends from segment data"""
        growth_analysis = {}

        # Defensive check for segment_data type
        if not isinstance(segment_data, dict):
            self.logger.logger.error(f"Expected dict for segment_data, got {type(segment_data)}: {segment_data}")
            return growth_analysis

        segment_revenues = segment_data.get('segment_data', [])
        if len(segment_revenues) >= 2:
            # Calculate growth rate between most recent periods
            latest = segment_revenues[0]
            previous = segment_revenues[1]

            if isinstance(latest, dict) and isinstance(previous, dict):
                if latest.get('revenue') and previous.get('revenue'):
                    growth_rate = ((latest['revenue'] - previous['revenue']) / previous['revenue']) * 100
                    growth_analysis['recent_growth_rate'] = f"{growth_rate:.1f}%"
                    growth_analysis['trend'] = "Growing" if growth_rate > 0 else "Declining"

        return growth_analysis

    def _analyze_competitive_position(self, segment_name: str, analysis_text: str) -> Dict[str, Any]:
        """Analyze competitive position from analysis text"""
        competitive_analysis = {}
        
        # Basic competitive context based on segment
        if segment_name.lower() == 'azure':
            competitive_analysis['main_competitors'] = ['AWS', 'Google Cloud']
            competitive_analysis['market_position'] = '#2 Cloud Provider'
        elif segment_name.lower() == 'aws':
            competitive_analysis['main_competitors'] = ['Microsoft Azure', 'Google Cloud']
            competitive_analysis['market_position'] = '#1 Cloud Provider'
        
        return competitive_analysis
